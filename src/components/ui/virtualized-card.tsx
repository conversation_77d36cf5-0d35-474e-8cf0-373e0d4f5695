'use client';

import React from 'react';

import { OrderCard } from '@/app/(app)/marketplace/order-card';
import { UserOrderCard } from '@/app/(app)/orders/user-order-card';
import { SecondaryMarketOrderCard } from '@/app/(app)/secondary-market/secondary-market-order-card';
import { GridItem } from '@/components/ui/virtualized-grid';
import type { CollectionEntity, OrderEntity } from '@/core.constants';

export type CardVariant = 'order' | 'user-order' | 'secondary-market';

interface BaseVirtualizedCardProps {
  order: OrderEntity;
  onClick: () => void;
  index: number;
  initialRenderedCount?: number;
  variant: CardVariant;
}

interface OrderCardVariantProps extends BaseVirtualizedCardProps {
  variant: 'order';
  collection: CollectionEntity | undefined;
  animated?: boolean;
}

interface UserOrderCardVariantProps extends BaseVirtualizedCardProps {
  variant: 'user-order';
  userRole: 'seller' | 'buyer';
}

interface SecondaryMarketCardVariantProps extends BaseVirtualizedCardProps {
  variant: 'secondary-market';
  collection: CollectionEntity | undefined;
  animated?: boolean;
}

export type VirtualizedCardProps =
  | OrderCardVariantProps
  | UserOrderCardVariantProps
  | SecondaryMarketCardVariantProps;

export const VirtualizedCard = (props: VirtualizedCardProps) => {
  const { order, onClick, index, initialRenderedCount = 15, variant } = props;

  const getItemId = () => {
    switch (variant) {
      case 'order':
        return `order-${order.id}`;
      case 'user-order':
        return `user-order-${order.id}`;
      case 'secondary-market':
        return `secondary-order-${order.id}`;
      default:
        return `card-never`;
    }
  };

  const renderCard = () => {
    switch (variant) {
      case 'order': {
        const orderProps = props;
        return (
          <OrderCard
            animated={orderProps.animated}
            order={order}
            collection={orderProps.collection}
            onClick={onClick}
          />
        );
      }

      case 'user-order': {
        const userOrderProps = props;
        return (
          <UserOrderCard
            order={order}
            userRole={userOrderProps.userRole}
            onClick={onClick}
          />
        );
      }

      case 'secondary-market': {
        const secondaryProps = props;
        return (
          <SecondaryMarketOrderCard
            animated={secondaryProps.animated}
            order={order}
            collection={secondaryProps.collection}
            onClick={onClick}
          />
        );
      }

      default:
        return null;
    }
  };

  return (
    <GridItem
      itemId={getItemId()}
      index={index}
      initialRenderedCount={initialRenderedCount}
    >
      {renderCard()}
    </GridItem>
  );
};
