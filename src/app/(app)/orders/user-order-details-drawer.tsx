'use client';

import { Avatar } from '@telegram-apps/telegram-ui';
import { Timestamp } from 'firebase/firestore';
import {
  AlertTriangle,
  Clock,
  ExternalLink,
  Gift,
  Loader2,
  User,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Drawer } from 'vaul';

import { getUserById } from '@/api/auth-api';
import { TgsOrImage } from '@/components/TgsOrImage';
import { TonLogo } from '@/components/TonLogo';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import type { OrderEntity, UserEntity } from '@/core.constants';
import { OrderStatus, TELEGRAM_BOT_URL } from '@/core.constants';
import { useRootContext } from '@/root-context';

import { CancelOrderDrawer } from './cancel-order-drawer';
import { SecondaryMarketOrderDrawer } from './secondary-market-order-drawer';

interface UserOrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userRole: 'seller' | 'buyer';
  onOrderUpdate: () => void;
}

export function UserOrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userRole,
  onOrderUpdate,
}: UserOrderDetailsDrawerProps) {
  const { collections, currentUser } = useRootContext();
  const [otherUser, setOtherUser] = useState<UserEntity | null>(null);
  const [loadingUser, setLoadingUser] = useState(false);
  const [showCancelDrawer, setShowCancelDrawer] = useState(false);
  const [showSecondaryMarketDrawer, setShowSecondaryMarketDrawer] =
    useState(false);
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [isFreezed, setIsFreezed] = useState<boolean>(false);
  const [localOrder, setLocalOrder] = useState<OrderEntity | null>(order);

  // Sync local order with prop changes
  useEffect(() => {
    setLocalOrder(order);
  }, [order]);

  const collection = collections.find((c) => c.id === localOrder?.collectionId);

  // Fetch other user info (buyer or seller)
  useEffect(() => {
    const fetchOtherUser = async () => {
      if (!localOrder) return;

      const otherUserId =
        userRole === 'seller' ? localOrder.buyerId : localOrder.sellerId;
      if (!otherUserId) return;

      setLoadingUser(true);
      try {
        const user = await getUserById(otherUserId);
        setOtherUser(user);
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setLoadingUser(false);
      }
    };

    if (open) {
      fetchOtherUser();
    }
  }, [localOrder, userRole, open]);

  // Calculate timers
  useEffect(() => {
    if (!order) return;

    const updateTimers = () => {
      if (!localOrder) return;

      const now = new Date();

      // Check freeze period
      if (collection?.launchedAt) {
        const launchedAt =
          collection.launchedAt instanceof Timestamp
            ? collection.launchedAt.toDate()
            : new Date(collection.launchedAt);
        const freezeEndDate = new Date(
          launchedAt.getTime() + 21 * 24 * 60 * 60 * 1000,
        );
        setIsFreezed(now < freezeEndDate);
      }

      // Calculate deadline countdown for paid and gift_sent_to_relayer statuses
      if (
        localOrder.deadline &&
        (localOrder.status === OrderStatus.PAID ||
          localOrder.status === OrderStatus.GIFT_SENT_TO_RELAYER)
      ) {
        const deadline =
          localOrder.deadline instanceof Timestamp
            ? localOrder.deadline.toDate()
            : new Date(localOrder.deadline);
        const timeDiff = deadline.getTime() - now.getTime();

        if (timeDiff > 0) {
          const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
          );
          const minutes = Math.floor(
            (timeDiff % (1000 * 60 * 60)) / (1000 * 60),
          );
          const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

          if (days > 0) {
            setTimeLeft(`${days}d ${hours}h ${minutes}m ${seconds}s`);
          } else if (hours > 0) {
            setTimeLeft(`${hours}h ${minutes}m ${seconds}s`);
          } else if (minutes > 0) {
            setTimeLeft(`${minutes}m ${seconds}s`);
          } else {
            setTimeLeft(`${seconds}s`);
          }
        } else {
          setTimeLeft('Expired');
        }
      } else {
        setTimeLeft('');
      }
    };

    updateTimers();
    const interval = setInterval(updateTimers, 1000);

    return () => clearInterval(interval);
  }, [order, localOrder, collection?.launchedAt]);

  const handleCancelOrder = () => {
    setShowCancelDrawer(true);
  };

  const handleOrderCancelled = () => {
    onOrderUpdate();
    onOpenChange(false);
  };

  const handleCreateSecondaryMarketOrder = () => {
    setShowSecondaryMarketDrawer(true);
  };

  const handleSecondaryMarketOrderCreated = (updatedPrice?: number) => {
    // Update the local order state with the new secondary market price
    if (localOrder && updatedPrice !== undefined) {
      setLocalOrder({
        ...localOrder,
        secondaryMarketPrice: updatedPrice,
      });
    }
    onOrderUpdate();
    setShowSecondaryMarketDrawer(false);
  };

  const getStatusBadge = () => {
    if (!localOrder) return null;

    switch (localOrder.status) {
      case OrderStatus.ACTIVE:
        return (
          <Badge
            variant="outline"
            className="bg-blue-500/20 text-blue-400 border-blue-500/30"
          >
            Active
          </Badge>
        );
      case OrderStatus.PAID:
        return (
          <Badge
            variant="outline"
            className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
          >
            Paid
          </Badge>
        );
      case OrderStatus.GIFT_SENT_TO_RELAYER:
        return (
          <Badge
            variant="outline"
            className="bg-purple-500/20 text-purple-400 border-purple-500/30"
          >
            Gift Sent
          </Badge>
        );
      case OrderStatus.FULFILLED:
        return (
          <Badge
            variant="outline"
            className="bg-green-500/20 text-green-400 border-green-500/30"
          >
            Fulfilled
          </Badge>
        );
      case OrderStatus.CANCELLED:
        return (
          <Badge
            variant="outline"
            className="bg-red-500/20 text-red-400 border-red-500/30"
          >
            Cancelled
          </Badge>
        );
      default:
        return null;
    }
  };

  const canCancelOrder =
    localOrder?.status === OrderStatus.PAID ||
    localOrder?.status === OrderStatus.ACTIVE ||
    localOrder?.status === OrderStatus.GIFT_SENT_TO_RELAYER;

  const canCreateSecondaryMarketOrder =
    localOrder?.status === OrderStatus.PAID &&
    currentUser?.id === localOrder?.buyerId &&
    localOrder?.sellerId &&
    localOrder?.buyerId;

  const hasSecondaryMarketPrice =
    localOrder?.secondaryMarketPrice && localOrder.secondaryMarketPrice > 0;

  if (!localOrder) return null;

  return (
    <>
      <Drawer.Root open={open} onOpenChange={onOpenChange}>
        <Drawer.Portal>
          <Drawer.Title />
          <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
          <Drawer.Content className="bg-[#17212b] flex flex-col rounded-t-[20px] h-[85vh] mt-24 fixed bottom-0 left-0 right-0 z-50">
            <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 overflow-y-auto">
              <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

              <div className="max-w-md mx-auto space-y-6">
                <div className="text-center space-y-2">
                  <h2 className="text-xl font-bold text-[#f5f5f5]">
                    Order Details
                  </h2>
                  {getStatusBadge()}
                </div>

                <div className="relative">
                  <div className="aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-[#232e3c] to-[#1a252f] p-8 border border-[#3a4a5c]/50">
                    {collection ? (
                      <TgsOrImage
                        isImage={false}
                        collectionId={collection.id}
                        imageProps={{
                          alt: collection.name || 'Order item',
                          fill: true,
                          className: 'object-contain drop-shadow-2xl',
                        }}
                        tgsProps={{
                          style: { height: '100%', width: '100%' },
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-[#3a4a5c] rounded flex items-center justify-center">
                        <div className="w-16 h-16 bg-[#17212b] rounded" />
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-center space-y-3">
                  <h1 className="text-2xl font-bold text-[#f5f5f5]">
                    {collection?.name ||
                      `Collection ${localOrder.collectionId}`}
                  </h1>

                  {hasSecondaryMarketPrice ? (
                    <div className="space-y-3">
                      <div>
                        <p className="text-[#708499] text-sm mb-1">
                          Primary Price
                        </p>
                        <div className="flex items-center justify-center gap-2 p-2 bg-[#232e3c] rounded-lg">
                          <TonLogo size={20} />
                          <span className="text-xl font-semibold text-[#f5f5f5]">
                            {localOrder.amount}
                          </span>
                          <span className="text-sm text-[#708499]">TON</span>
                        </div>
                      </div>

                      <div>
                        <p className="text-[#6ab2f2] text-sm mb-1">
                          Secondary Market Price
                        </p>
                        <div className="flex items-center justify-center gap-2 p-3 bg-[#17212b] rounded-lg border border-[#6ab2f2]/20">
                          <TonLogo size={24} />
                          <span className="text-3xl font-bold text-[#6ab2f2]">
                            {localOrder.secondaryMarketPrice}
                          </span>
                          <span className="text-lg text-[#708499]">TON</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2 p-3 bg-[#17212b] rounded-lg">
                      <TonLogo size={24} />
                      <span className="text-3xl font-bold text-[#f5f5f5]">
                        {localOrder.amount}
                      </span>
                      <span className="text-lg text-[#708499]">TON</span>
                    </div>
                  )}
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
                    <span className="text-[#f5f5f5] font-medium">
                      Order Number
                    </span>
                    <span className="text-[#6ab2f2] font-semibold">
                      #{localOrder.number || localOrder.id?.slice(-6)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
                    <span className="text-[#f5f5f5] font-medium">
                      Your Role
                    </span>
                    <span className="text-[#6ab2f2] font-semibold capitalize">
                      {userRole}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
                    <span className="text-[#f5f5f5] font-medium">Status</span>
                    <span className="text-[#6ab2f2] font-semibold capitalize">
                      {localOrder.status === OrderStatus.GIFT_SENT_TO_RELAYER
                        ? 'Gift Sent'
                        : localOrder.status.replace('_', ' ')}
                    </span>
                  </div>
                  {localOrder.createdAt && (
                    <div className="flex justify-between items-center py-2">
                      <span className="text-[#f5f5f5] font-medium">
                        Created
                      </span>
                      <span className="text-[#6ab2f2] font-semibold">
                        {(localOrder.createdAt instanceof Timestamp
                          ? localOrder.createdAt.toDate()
                          : new Date(localOrder.createdAt)
                        ).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </div>

                {(localOrder.status === OrderStatus.PAID ||
                  localOrder.status === OrderStatus.GIFT_SENT_TO_RELAYER) && (
                  <div className="space-y-4">
                    {localOrder.deadline && timeLeft ? (
                      <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Clock className="w-5 h-5 text-orange-400" />
                          <span className="text-orange-400 font-semibold">
                            {localOrder.status === OrderStatus.PAID &&
                              userRole === 'seller' &&
                              'Time to Send Gift'}
                            {localOrder.status === OrderStatus.PAID &&
                              userRole === 'buyer' &&
                              'Seller Deadline'}
                            {localOrder.status ===
                              OrderStatus.GIFT_SENT_TO_RELAYER &&
                              userRole === 'buyer' &&
                              'Time to Claim Gift'}
                            {localOrder.status ===
                              OrderStatus.GIFT_SENT_TO_RELAYER &&
                              userRole === 'seller' &&
                              'Buyer Deadline'}
                          </span>
                        </div>
                        <div className="text-center">
                          <div className="text-3xl font-mono font-bold text-[#f5f5f5] mb-2">
                            {timeLeft}
                          </div>
                          <div className="text-sm text-[#708499]">
                            {userRole === 'seller' &&
                              localOrder.status === OrderStatus.PAID &&
                              'Send gift to relayer or lose collateral'}
                            {userRole === 'buyer' &&
                              localOrder.status === OrderStatus.PAID &&
                              'Seller must send gift or lose collateral'}
                            {userRole === 'buyer' &&
                              localOrder.status ===
                                OrderStatus.GIFT_SENT_TO_RELAYER &&
                              'Claim gift from relayer or lose collateral'}
                            {userRole === 'seller' &&
                              localOrder.status ===
                                OrderStatus.GIFT_SENT_TO_RELAYER &&
                              'Buyer must claim gift or lose collateral'}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Clock className="w-5 h-5 text-orange-400" />
                          <span className="text-orange-400 font-semibold">
                            Waiting
                          </span>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-[#708499]">
                            Gift will become transferable soon
                          </div>
                        </div>
                      </div>
                    )}

                    {isFreezed && userRole === 'seller' && (
                      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-2xl p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="w-5 h-5 text-yellow-400" />
                          <span className="text-yellow-400 font-semibold">
                            Freeze Period Active
                          </span>
                        </div>
                        <p className="text-[#708499] text-sm">
                          Collection items cannot be transferred yet. Wait for
                          the freeze period to end.
                        </p>
                      </div>
                    )}

                    {!isFreezed &&
                      userRole === 'seller' &&
                      !localOrder.deadline && (
                        <div className="bg-blue-500/10 border border-blue-500/20 rounded-2xl p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Clock className="w-5 h-5 text-blue-400" />
                            <span className="text-blue-400 font-semibold">
                              Waiting for Transfer
                            </span>
                          </div>
                          <p className="text-[#708499] text-sm">
                            Wait until the collection item becomes transferable.
                          </p>
                        </div>
                      )}

                    {!isFreezed &&
                      userRole === 'seller' &&
                      localOrder.deadline && (
                        <div className="bg-green-500/10 border border-green-500/20 rounded-2xl p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Gift className="w-5 h-5 text-green-400" />
                            <span className="text-green-400 font-semibold">
                              Ready to Send
                            </span>
                          </div>
                          <p className="text-[#708499] text-sm">
                            You can now send the gift to the relayer.
                          </p>
                        </div>
                      )}
                  </div>
                )}

                {localOrder.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
                  userRole === 'buyer' && (
                    <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-2xl p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Gift className="w-5 h-5 text-purple-400" />
                        <span className="text-purple-400 font-semibold">
                          Gift Ready!
                        </span>
                      </div>
                      <p className="text-[#708499] text-sm mb-4">
                        Your gift has been sent to the relayer. Please visit the
                        bot to claim your gift.
                      </p>
                      <Button
                        size="sm"
                        className="w-full bg-purple-500 hover:bg-purple-600 text-white rounded-xl"
                        onClick={() => window.open(TELEGRAM_BOT_URL, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Open Bot to Claim
                      </Button>
                    </div>
                  )}

                {localOrder.status === OrderStatus.CANCELLED &&
                  localOrder.owned_gift_id &&
                  userRole === 'seller' && (
                    <div className="bg-orange-500/10 border border-orange-500/20 rounded-2xl p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <AlertTriangle className="w-5 h-5 text-orange-400" />
                        <span className="text-orange-400 font-semibold">
                          Gift Refund Available
                        </span>
                      </div>
                      <p className="text-[#708499] text-sm mb-4">
                        Go to the relayer to refund your gift.
                      </p>
                      <Button
                        size="sm"
                        className="w-full bg-orange-500 hover:bg-orange-600 text-white rounded-xl"
                        onClick={() => window.open(TELEGRAM_BOT_URL, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Open Bot for Refund
                      </Button>
                    </div>
                  )}

                {(localOrder.status === OrderStatus.PAID ||
                  localOrder.status === OrderStatus.GIFT_SENT_TO_RELAYER) && (
                  <div className="space-y-3">
                    <h3 className="font-semibold text-[#f5f5f5] text-center">
                      {userRole === 'seller' ? 'Buyer' : 'Seller'} Information
                    </h3>
                    {loadingUser ? (
                      <div className="flex items-center justify-center gap-3 p-4 bg-[#232e3c] rounded-2xl">
                        <Loader2 className="w-5 h-5 animate-spin text-[#6ab2f2]" />
                        <span className="text-[#708499]">
                          Loading user info...
                        </span>
                      </div>
                    ) : otherUser ? (
                      <div className="flex items-center gap-4 p-4 bg-[#232e3c] rounded-2xl">
                        {otherUser.photoURL ? (
                          <Avatar
                            size={48}
                            src={otherUser.photoURL}
                            className="ring-2 ring-[#6ab2f2]/20"
                          />
                        ) : (
                          <div className="w-12 h-12 bg-[#3a4a5c] rounded-full flex items-center justify-center ring-2 ring-[#6ab2f2]/20">
                            <User className="w-6 h-6 text-[#708499]" />
                          </div>
                        )}
                        <div className="flex-1">
                          <p className="text-[#f5f5f5] font-semibold text-lg">
                            {otherUser.displayName ||
                              otherUser.name ||
                              'Anonymous User'}
                          </p>
                          <p className="text-[#6ab2f2] text-sm font-medium">
                            {userRole === 'seller' ? 'Buyer' : 'Seller'}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="p-4 bg-[#232e3c] rounded-2xl text-center">
                        <p className="text-[#708499] text-sm">
                          No {userRole === 'seller' ? 'buyer' : 'seller'}{' '}
                          assigned yet
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {(canCreateSecondaryMarketOrder || canCancelOrder) && (
                  <div className="pt-6 border-t border-[#3a4a5c]/30 space-y-3">
                    {canCreateSecondaryMarketOrder && (
                      <Button
                        onClick={handleCreateSecondaryMarketOrder}
                        className="w-full rounded-xl py-3 font-semibold bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white"
                      >
                        {hasSecondaryMarketPrice
                          ? 'Update Resale Order'
                          : 'Create Resale Order'}
                      </Button>
                    )}
                    {canCancelOrder && (
                      <Button
                        variant="destructive"
                        onClick={handleCancelOrder}
                        className="w-full rounded-xl py-3 font-semibold"
                      >
                        Cancel Order
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>

      <CancelOrderDrawer
        open={showCancelDrawer}
        onOpenChange={setShowCancelDrawer}
        order={localOrder}
        onOrderCancelled={handleOrderCancelled}
      />

      <SecondaryMarketOrderDrawer
        open={showSecondaryMarketDrawer}
        onOpenChange={setShowSecondaryMarketDrawer}
        order={localOrder}
        onOrderCreated={handleSecondaryMarketOrderCreated}
      />
    </>
  );
}
