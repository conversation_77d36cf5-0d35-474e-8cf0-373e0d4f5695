import { Clock } from 'lucide-react';
import type { OrderEntity } from '@/core.constants';
import { getDeadlineTitle, getDeadlineDescription } from '@/utils/order-status-utils';

interface UserOrderDeadlineSectionProps {
  order: OrderEntity;
  userRole: 'seller' | 'buyer';
  timeLeft: string;
}

export function UserOrderDeadlineSection({
  order,
  userRole,
  timeLeft
}: UserOrderDeadlineSectionProps) {
  if (!timeLeft) {
    return (
      <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-4">
        <div className="flex items-center gap-2 mb-3">
          <Clock className="w-5 h-5 text-orange-400" />
          <span className="text-orange-400 font-semibold">Waiting</span>
        </div>
        <div className="text-center">
          <div className="text-sm text-[#708499]">
            Gift will become transferable soon
          </div>
        </div>
      </div>
    );
  }

  const title = getDeadlineTitle(order, userRole);
  const description = getDeadlineDescription(order, userRole);

  return (
    <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-4">
      <div className="flex items-center gap-2 mb-3">
        <Clock className="w-5 h-5 text-orange-400" />
        <span className="text-orange-400 font-semibold">{title}</span>
      </div>
      <div className="text-center">
        <div className="text-3xl font-mono font-bold text-[#f5f5f5] mb-2">
          {timeLeft}
        </div>
        {description && (
          <div className="text-sm text-[#708499]">{description}</div>
        )}
      </div>
    </div>
  );
}
