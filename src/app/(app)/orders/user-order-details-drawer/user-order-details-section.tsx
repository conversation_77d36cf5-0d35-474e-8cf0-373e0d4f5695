import { Timestamp } from 'firebase/firestore';
import type { OrderEntity } from '@/core.constants';
import { OrderStatus } from '@/core.constants';
import { getOrderDisplayNumber } from '@/utils/order-utils';

interface UserOrderDetailsSectionProps {
  order: OrderEntity;
  userRole: 'seller' | 'buyer';
}

export function UserOrderDetailsSection({ order, userRole }: UserOrderDetailsSectionProps) {
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
        <span className="text-[#f5f5f5] font-medium">Order Number</span>
        <span className="text-[#6ab2f2] font-semibold">
          {getOrderDisplayNumber(order)}
        </span>
      </div>
      
      <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
        <span className="text-[#f5f5f5] font-medium">Your Role</span>
        <span className="text-[#6ab2f2] font-semibold capitalize">
          {userRole}
        </span>
      </div>
      
      <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
        <span className="text-[#f5f5f5] font-medium">Status</span>
        <span className="text-[#6ab2f2] font-semibold capitalize">
          {order.status === OrderStatus.GIFT_SENT_TO_RELAYER
            ? 'Gift Sent'
            : order.status.replace('_', ' ')}
        </span>
      </div>
      
      {order.createdAt && (
        <div className="flex justify-between items-center py-2">
          <span className="text-[#f5f5f5] font-medium">Created</span>
          <span className="text-[#6ab2f2] font-semibold">
            {(order.createdAt instanceof Timestamp
              ? order.createdAt.toDate()
              : new Date(order.createdAt)
            ).toLocaleDateString()}
          </span>
        </div>
      )}
    </div>
  );
}
