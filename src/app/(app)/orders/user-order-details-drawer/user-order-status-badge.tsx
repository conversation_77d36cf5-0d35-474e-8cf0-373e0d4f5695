import { Badge } from '@/components/ui/badge';
import type { OrderEntity } from '@/core.constants';
import { getStatusConfig } from '@/utils/order-status-utils';

interface UserOrderStatusBadgeProps {
  order: OrderEntity;
}

export function UserOrderStatusBadge({ order }: UserOrderStatusBadgeProps) {
  const statusConfig = getStatusConfig(order.status);

  return (
    <Badge variant="outline" className={statusConfig.className}>
      {statusConfig.label}
    </Badge>
  );
}
