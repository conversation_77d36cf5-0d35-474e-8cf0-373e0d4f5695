import { AlertTriangle, Clock, Gift, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { OrderEntity } from '@/core.constants';
import { OrderStatus, TELEGRAM_BOT_URL } from '@/core.constants';
import {
  shouldShowFreezeWarning,
  shouldShowGiftReadySection,
  shouldShowGiftRefundSection
} from '@/utils/order-utils';

interface UserOrderStatusAlertsProps {
  order: OrderEntity;
  userRole: 'seller' | 'buyer';
  isFreezed: boolean;
}

export function UserOrderStatusAlerts({
  order,
  userRole,
  isFreezed
}: UserOrderStatusAlertsProps) {
  const showFreezeWarning = shouldShowFreezeWarning(order, userRole, isFreezed);
  const showGiftReady = shouldShowGiftReadySection(order, userRole);
  const showGiftRefund = shouldShowGiftRefundSection(order, userRole);

  if (!showFreezeWarning && !showGiftReady && !showGiftRefund) {
    return null;
  }

  return (
    <div className="space-y-4">
      {showFreezeWarning && (
        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-2xl p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-yellow-400" />
            <span className="text-yellow-400 font-semibold">
              Freeze Period Active
            </span>
          </div>
          <p className="text-[#708499] text-sm">
            Collection items cannot be transferred yet. Wait for the freeze period to end.
          </p>
        </div>
      )}

      {!isFreezed && userRole === 'seller' && !order.deadline && (
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-2xl p-4">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="w-5 h-5 text-blue-400" />
            <span className="text-blue-400 font-semibold">
              Waiting for Transfer
            </span>
          </div>
          <p className="text-[#708499] text-sm">
            Wait until the collection item becomes transferable.
          </p>
        </div>
      )}

      {!isFreezed && userRole === 'seller' && order.deadline && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-2xl p-4">
          <div className="flex items-center gap-2 mb-2">
            <Gift className="w-5 h-5 text-green-400" />
            <span className="text-green-400 font-semibold">Ready to Send</span>
          </div>
          <p className="text-[#708499] text-sm">
            You can now send the gift to the relayer.
          </p>
        </div>
      )}

      {showGiftReady && (
        <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-2xl p-4">
          <div className="flex items-center gap-2 mb-3">
            <Gift className="w-5 h-5 text-purple-400" />
            <span className="text-purple-400 font-semibold">Gift Ready!</span>
          </div>
          <p className="text-[#708499] text-sm mb-4">
            Your gift has been sent to the relayer. Please visit the bot to claim your gift.
          </p>
          <Button
            size="sm"
            className="w-full bg-purple-500 hover:bg-purple-600 text-white rounded-xl"
            onClick={() => window.open(TELEGRAM_BOT_URL, '_blank')}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Open Bot to Claim
          </Button>
        </div>
      )}

      {showGiftRefund && (
        <div className="bg-orange-500/10 border border-orange-500/20 rounded-2xl p-4">
          <div className="flex items-center gap-2 mb-3">
            <AlertTriangle className="w-5 h-5 text-orange-400" />
            <span className="text-orange-400 font-semibold">
              Gift Refund Available
            </span>
          </div>
          <p className="text-[#708499] text-sm mb-4">
            Go to the relayer to refund your gift.
          </p>
          <Button
            size="sm"
            className="w-full bg-orange-500 hover:bg-orange-600 text-white rounded-xl"
            onClick={() => window.open(TELEGRAM_BOT_URL, '_blank')}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Open Bot for Refund
          </Button>
        </div>
      )}
    </div>
  );
}
