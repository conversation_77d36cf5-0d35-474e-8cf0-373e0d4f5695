import { useEffect, useState } from 'react';
import type { OrderEntity, CollectionEntity } from '@/core.constants';
import {
  calculateFreezeStatus,
  calculateDeadlineInfo,
  formatTimeLeft,
  shouldShowDeadlineTimer,
  type TimerState
} from '@/utils/order-utils';

interface UseOrderTimersProps {
  order: OrderEntity | null;
  collection: CollectionEntity | null;
}

export function useOrderTimers({ order, collection }: UseOrderTimersProps): TimerState {
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [isFreezed, setIsFreezed] = useState<boolean>(false);

  useEffect(() => {
    if (!order) return;

    const updateTimers = () => {
      // Update freeze status
      const freezeStatus = calculateFreezeStatus(collection);
      setIsFreezed(freezeStatus);

      // Update deadline countdown
      if (shouldShowDeadlineTimer(order)) {
        const deadlineInfo = calculateDeadlineInfo(order.deadline!);
        const formattedTime = formatTimeLeft(deadlineInfo);
        setTimeLeft(formattedTime);
      } else {
        setTimeLeft('');
      }
    };

    updateTimers();
    const interval = setInterval(updateTimers, 1000);

    return () => clearInterval(interval);
  }, [order, collection?.launchedAt]);

  return { timeLeft, isFreezed };
}
