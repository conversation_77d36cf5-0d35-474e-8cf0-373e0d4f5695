import { useEffect, useState } from 'react';
import { getUserById } from '@/api/auth-api';
import type { OrderEntity, UserEntity } from '@/core.constants';
import { getOtherUserId } from '@/utils/order-utils';

interface UseOtherUserProps {
  order: OrderEntity | null;
  userRole: 'seller' | 'buyer';
  isOpen: boolean;
}

export function useOtherUser({ order, userRole, isOpen }: UseOtherUserProps) {
  const [otherUser, setOtherUser] = useState<UserEntity | null>(null);
  const [loadingUser, setLoadingUser] = useState(false);

  useEffect(() => {
    const fetchOtherUser = async () => {
      if (!order) return;

      const otherUserId = getOtherUserId(order, userRole);
      if (!otherUserId) return;

      setLoadingUser(true);
      try {
        const user = await getUserById(otherUserId);
        setOtherUser(user);
      } catch (error) {
        console.error('Error fetching user:', error);
        setOtherUser(null);
      } finally {
        setLoadingUser(false);
      }
    };

    if (isOpen) {
      fetchOtherUser();
    } else {
      setOtherUser(null);
      setLoadingUser(false);
    }
  }, [order, userRole, isOpen]);

  return { otherUser, loadingUser };
}
